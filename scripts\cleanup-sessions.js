/**
 * Standalone session cleanup script
 * Can be run as a cron job or scheduled task
 * 
 * Usage:
 * node scripts/cleanup-sessions.js
 * 
 * Or add to package.json scripts:
 * "cleanup-sessions": "node scripts/cleanup-sessions.js"
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupExpiredSessions() {
  try {
    console.log('Starting session cleanup...');
    const now = new Date();
    
    // Clean up expired sessions
    const expiredSessions = await prisma.session.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    });

    // Clean up expired verification tokens
    const expiredTokens = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: now
        }
      }
    });

    console.log('Session cleanup completed:', {
      expiredSessions: expiredSessions.count,
      expiredTokens: expiredTokens.count,
      timestamp: now.toISOString()
    });

    return {
      expiredSessions: expiredSessions.count,
      expiredTokens: expiredTokens.count,
      success: true
    };
  } catch (error) {
    console.error('Session cleanup failed:', error);
    return {
      expiredSessions: 0,
      expiredTokens: 0,
      success: false,
      error: error.message
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanupExpiredSessions()
    .then((result) => {
      console.log('Cleanup result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Cleanup script failed:', error);
      process.exit(1);
    });
}

module.exports = { cleanupExpiredSessions };
