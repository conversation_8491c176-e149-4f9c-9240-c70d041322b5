import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Protected routes that require authentication
const protectedRoutes = ['/dashboard'];
// Public routes that should redirect to dashboard if already authenticated
const publicAuthRoutes = ['/login'];

export async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // Temporarily disable middleware to test login page
  console.log('Middleware - DISABLED for testing - Path:', path);
  return NextResponse.next();

  /*
  // Use NextAuth JWT token for authentication
  const token = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET
  });
  const isAuthenticated = !!token;

  // Debug logging for production issues
  console.log('Middleware - Path:', path, 'Authenticated:', isAuthenticated, 'Token exists:', !!token);
  console.log('Middleware - NEXTAUTH_SECRET exists:', !!process.env.NEXTAUTH_SECRET);
  console.log('Middleware - Token details:', token ? { email: token.email, exp: token.exp } : 'No token');
  */

  /*
  // Redirect to login if accessing protected route without authentication
  if (protectedRoutes.some(route => path.startsWith(route)) && !isAuthenticated) {
    console.log('Middleware - Redirecting to login from protected route:', path);
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Redirect to dashboard if accessing login page while authenticated
  if (publicAuthRoutes.includes(path) && isAuthenticated) {
    console.log('Middleware - Redirecting to dashboard from login page');
    const response = NextResponse.redirect(new URL('/dashboard', req.url));
    // Add cache control headers to prevent caching of redirects
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
  }
  */

  /*
  // For login page, add no-cache headers when not authenticated
  if (publicAuthRoutes.includes(path) && !isAuthenticated) {
    const response = NextResponse.next();
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
  }

  return NextResponse.next();
  */
}

// Configure which routes middleware should run on
export const config = {
  matcher: [
    // Include the specific paths you need middleware for
    '/dashboard',
    '/dashboard/:path*',
    '/login',
    '/profile',
    '/profile/:path*'
    // Add other paths as needed
  ],
};


