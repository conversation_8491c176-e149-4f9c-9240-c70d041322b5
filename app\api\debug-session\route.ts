import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth-config';
import { prisma } from '@/app/lib/prisma';
import { NextResponse } from 'next/server';
import { getSessionStats } from '@/app/lib/session-cleanup';

export async function GET() {
  try {
    // Get server session
    const session = await getServerSession(authOptions);
    
    // Get all users from database
    const users = await prisma.user.findMany({
      include: {
        sessions: true,
        accounts: true
      }
    });
    
    // Get all sessions from database
    const sessions = await prisma.session.findMany({
      include: {
        user: true
      }
    });
    
    // Get session statistics
    const sessionStats = await getSessionStats();

    return NextResponse.json({
      serverSession: session,
      databaseUsers: users,
      databaseSessions: sessions,
      sessionStats,
      timestamp: new Date().toISOString(),
      note: "JWT session strategy - database sessions are obsolete and should be cleared"
    });
  } catch (error) {
    console.error('Debug session error:', error);
    return NextResponse.json({
      error: 'Failed to debug session',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Add a POST endpoint to clear old database sessions
export async function POST() {
  try {
    // Clear all database sessions since we're using JWT now
    const deletedSessions = await prisma.session.deleteMany({});

    return NextResponse.json({
      message: 'Database sessions cleared - users will need to re-authenticate',
      deletedCount: deletedSessions.count,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Clear sessions error:', error);
    return NextResponse.json(
      { error: 'Failed to clear sessions' },
      { status: 500 }
    );
  }
}
