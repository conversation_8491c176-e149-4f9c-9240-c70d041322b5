{"name": "react-nextjs-learn-auth", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "cleanup-sessions": "node scripts/cleanup-sessions.js"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "next": "15.3.5", "next-auth": "^4.24.5", "prisma": "^6.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}