import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { dirname } from 'path';

/**
 * Initialize database if it doesn't exist
 * This is particularly important for SQLite in serverless environments
 */
export async function initializeDatabase() {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  // Only handle SQLite databases
  if (!databaseUrl.startsWith('file:')) {
    console.log('Non-SQLite database detected, skipping initialization');
    return;
  }

  // Extract file path from database URL
  const dbPath = databaseUrl.replace('file:', '');
  
  // Check if database file exists
  if (!existsSync(dbPath)) {
    console.log(`Database file ${dbPath} does not exist, creating...`);
    
    try {
      // Ensure directory exists
      const dbDir = dirname(dbPath);
      execSync(`mkdir -p "${dbDir}"`, { stdio: 'inherit' });
      
      // Create and initialize database with Prisma
      execSync('npx prisma db push', { 
        stdio: 'inherit',
        env: { ...process.env, DATABASE_URL: databaseUrl }
      });
      
      console.log(`Database ${dbPath} created and initialized successfully`);
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  } else {
    console.log(`Database ${dbPath} already exists`);
  }
}

/**
 * Ensure database is ready for use
 * Call this before any database operations in serverless environments
 */
export async function ensureDatabaseReady() {
  try {
    await initializeDatabase();
  } catch (error) {
    console.error('Database initialization failed:', error);
    // In production, you might want to handle this more gracefully
    throw error;
  }
}
